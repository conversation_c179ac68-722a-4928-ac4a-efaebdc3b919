<template>
  <div class="page-container">
    <!-- 搜索筛选区域 -->
    <div class="search-section">
      <div class="layui-card main-card">
        <div class="layui-card-header">店铺管理</div>
        <div class="layui-card-body">
          <div class="list_search">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-input
                  v-model="searchForm.storeName"
                  placeholder="店铺名称"
                  clearable
                />
              </el-col>
              <el-col :span="8">
                <el-select
                  v-model="searchForm.online"
                  placeholder="是否在线"
                  clearable
                  style="width: 100%"
                >
                  <el-option label="在线" value="online" />
                  <el-option label="离线" value="offline" />
                </el-select>
              </el-col>
              <el-col :span="8">
                <el-button type="primary" @click="handleSearch" class="btn2">
                  <el-icon><Search /></el-icon>
                  搜索
                </el-button>
                <el-button @click="handleReset" class="btn6">
                  <el-icon><Refresh /></el-icon>
                  重置
                </el-button>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
    </div>

    <!-- 表格和分页容器 -->
    <div class="table-section">
      <div class="layui-card main-card table-card">
        <div class="layui-card-body">
          <!-- 表格容器 -->
          <div class="table-wrapper">
            <el-table
              :data="tableData"
              style="width: 100%"
              stripe
              border
              :max-height="tableMaxHeight"
              class="stable-table"
              @row-contextmenu="handleRowContextMenu"
            >
              <el-table-column prop="name" label="店铺名称" min-width="150" />
              <el-table-column prop="todayReception" label="今日接待" min-width="100" />
              <el-table-column prop="pointsConsumed" label="消耗点数" min-width="100" />
              <el-table-column prop="incomingCount" label="进线次数" min-width="100" />
              <el-table-column prop="transferCount" label="转接次数" min-width="100" />
              <el-table-column prop="account" label="所属账号" min-width="120" />
              <el-table-column prop="createTime" label="入库时间" min-width="180" />
              <el-table-column prop="online" label="是否在线" min-width="100">
                <template #default="scope">
                  <el-tag
                    :type="scope.row.online === '在线' ? 'success' : 'info'"
                    size="small"
                  >
                    {{ scope.row.online }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 分页容器 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="total"
              layout="prev, pager, next"
              prev-text="上一页"
              next-text="下一页"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              class="layui-pagination"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 右键菜单 -->
    <StoreContextMenu
      ref="contextMenuRef"
      @menu-click="handleContextMenuClick"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'
import StoreContextMenu from '../components/ui/StoreContextMenu.vue'

// 搜索表单
const searchForm = ref({
  storeName: '',
  online: ''
})

// 获取默认店铺数据（用于调试）
const getDefaultStoreData = () => [
  {
    name: '科沃斯',
    todayReception: 0,
    pointsConsumed: 0,
    incomingCount: 0,
    transferCount: 0,
    account: '10086',
    createTime: '2025/7/5 21:04:34',
    online: '离线'
  },
  {
    name: '小米有品',
    todayReception: 15,
    pointsConsumed: 120,
    incomingCount: 25,
    transferCount: 3,
    account: '10087',
    createTime: '2025/7/5 21:04:34',
    online: '在线'
  },
  {
    name: '华为商城',
    todayReception: 28,
    pointsConsumed: 200,
    incomingCount: 45,
    transferCount: 5,
    account: '10088',
    createTime: '2025/7/5 21:04:34',
    online: '在线'
  },
  {
    name: '京东自营',
    todayReception: 52,
    pointsConsumed: 380,
    incomingCount: 78,
    transferCount: 8,
    account: '10089',
    createTime: '2025/7/5 21:04:34',
    online: '在线'
  },
  {
    name: '天猫旗舰店',
    todayReception: 0,
    pointsConsumed: 0,
    incomingCount: 0,
    transferCount: 0,
    account: '10090',
    createTime: '2025/7/5 21:04:34',
    online: '离线'
  }
]

// 表格数据（初始为空）
const tableData = ref([])

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 右键菜单
const contextMenuRef = ref(null)

// 计算表格最大高度
const tableMaxHeight = computed(() => {
  // 基础高度：视口高度减去头部、搜索区域、分页区域等固定高度
  const baseHeight = window.innerHeight - 300 // 预留300px给其他元素
  return Math.max(400, Math.min(600, baseHeight)) // 最小400px，最大600px
})

// 搜索
const handleSearch = () => {
  console.log('搜索:', searchForm.value)

  // 调用客户端函数
  if (window.g_click) {
    window.g_click({
      request: JSON.stringify({
        action: "店铺管理搜索按钮",
        text: JSON.stringify(searchForm.value)
      })
    })
  }

  ElMessage.success('搜索功能待实现')
}

// 重置
const handleReset = () => {
  searchForm.value = {
    storeName: '',
    online: ''
  }

  // 调用客户端函数
  if (window.g_click) {
    window.g_click({
      request: JSON.stringify({
        action: "店铺管理重置按钮"
      })
    })
  }

  //ElMessage.info('已重置搜索条件')
}

// 右键菜单处理
const handleRowContextMenu = (row, column, event) => {
  event.preventDefault()

  if (contextMenuRef.value) {
    contextMenuRef.value.show(event.clientX, event.clientY, row)
  }
}

// 处理右键菜单点击
const handleContextMenuClick = ({ action, data }) => {
  // 这里的逻辑已经在StoreContextMenu组件中处理了
  // 包括调用window.g_click和显示确认对话框
}







// 分页事件
const handleSizeChange = (val) => {
  pageSize.value = val
  console.log(`每页 ${val} 条`)

  // 调用客户端函数
  if (window.g_click) {
    window.g_click({
      request: JSON.stringify({
        action: "店铺管理分页大小改变",
        pageSize: val
      })
    })
  }
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  console.log(`当前页: ${val}`)

  // 调用客户端函数
  if (window.g_click) {
    window.g_click({
      request: JSON.stringify({
        action: "店铺管理分页按钮",
        page: val
      })
    })
  }
}

// 设置全局变量
const setupGlobalVariables = () => {
  // 设置表格数据到全局变量
  window.table_dianpu = tableData.value
  window.table_dianpu_currentPage = currentPage.value
  window.table_dianpu_pageSize = pageSize.value
  window.table_dianpu_total = total.value
}

// 监听数据变化并更新全局变量
watch([tableData, currentPage, pageSize, total], () => {
  setupGlobalVariables()
}, { deep: true })

// 从全局变量更新数据
const updateFromGlobalData = () => {
  if (window.table_dianpu && Array.isArray(window.table_dianpu)) {
    tableData.value = window.table_dianpu
  }
  if (window.table_dianpu_currentPage) {
    currentPage.value = window.table_dianpu_currentPage
  }
  if (window.table_dianpu_pageSize) {
    pageSize.value = window.table_dianpu_pageSize
  }
     if (window.table_dianpu_total) {
    total.value = window.table_dianpu_total
  }
}

// 清空店铺数据函数
const clearStoreData = () => {
  tableData.value = []
  total.value = 0
  currentPage.value = 1
}

// 暴露更新函数到全局
window.updateStoreData = updateFromGlobalData

onMounted(() => {
  // 检查是否有全局数据，如果有就使用，没有就保持空白
  if (window.table_dianpu && window.table_dianpu.length > 0) {
    // 有数据时从全局变量更新
    updateFromGlobalData()
  } else {
    // 没有数据时清空并初始化全局变量
    clearStoreData()
    setupGlobalVariables()
  }
})
</script>

<style scoped>
/* 页面容器布局 */
.page-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 64px); /* 减去头部导航高度，贴近底部 */
  padding: 20px 0px 20px 20px; /* 底部添加padding */
  box-sizing: border-box;
  margin-top: -32px;
}

.search-section {
  flex-shrink: 0;
  margin-bottom: 15px;
}

.table-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 重要：允许flex子元素收缩 */
}

.table-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.table-card .layui-card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  padding: 16px;
}

/* 表格容器 */
.table-wrapper {
  flex: 1;
  min-height: 0;
  margin-bottom: 16px;
}

.stable-table {
  height: 100%;
}

/* 分页容器 */
.pagination-wrapper {
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px 0;
  border-top: 1px solid #e5e7eb;
  
}

/* Element Plus 样式覆盖 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__header) {
  background-color: #f8f9fa;
}

:deep(.stable-table .el-table__body-wrapper) {
  overflow-y: auto;
  max-height: calc(100% - 60px); /* 减去表头高度 */
}

:deep(.el-button.btn2) {
  background-color: #7748F8;
  border-color: #7748F8;
  color: #fff;
}

:deep(.el-button.btn2:hover) {
  background-color: #6639e6;
  border-color: #6639e6;
}

:deep(.el-button.btn6) {
  border: 1px solid #9494AA;
  color: #9494AA;
  
}

:deep(.el-button.btn6:hover) {
  background-color: #f5f5f5;
}

/* Layui 风格分页样式 */
:deep(.layui-pagination) {
  justify-content: center;
}

:deep(.layui-pagination .btn-next),
:deep(.layui-pagination .btn-prev) {
  background-color: #fff;
  border: 1px solid #e2e2e2;
  color: #333;
  border-radius: 2px;
  padding: 0 15px;
  height: 30px;
  line-height: 28px;
  font-size: 12px;
}

:deep(.layui-pagination .btn-next:hover),
:deep(.layui-pagination .btn-prev:hover) {
  color: #7748F8;
  border-color: #7748F8;
}

:deep(.layui-pagination .btn-next.is-disabled),
:deep(.layui-pagination .btn-prev.is-disabled) {
  color: #c0c4cc;
  background-color: #fff;
  border-color: #e2e2e2;
  cursor: not-allowed;
}

:deep(.layui-pagination .el-pager li) {
  background-color: #fff;
  border: 1px solid #e2e2e2;
  color: #333;
  border-radius: 2px;
  margin: 0 2px;
  min-width: 30px;
  height: 30px;
  line-height: 28px;
  font-size: 12px;
}

:deep(.layui-pagination .el-pager li:hover) {
  color: #7748F8;
  border-color: #7748F8;
}

:deep(.layui-pagination .el-pager li.is-active) {
  background-color: #7748F8;
  border-color: #7748F8;
  color: #fff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 10px;
    height: calc(100vh - 60px); /* 移动端贴近底部 */
  }

  .pagination-wrapper {
    padding: 12px 0;
  }

  :deep(.layui-pagination) {
    justify-content: center;
  }

  :deep(.el-pagination) {
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .table-wrapper {
    overflow-x: auto;
  }

  :deep(.stable-table) {
    min-width: 800px;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 10px;
    height: calc(100vh - 60px); /* 移动端贴近底部 */
  }

  .pagination-wrapper {
    padding: 12px 0;
  }

  :deep(.layui-pagination) {
    justify-content: center;
  }

  :deep(.el-pagination) {
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .table-wrapper {
    overflow-x: auto;
  }

  :deep(.stable-table) {
    min-width: 800px;
  }
}
</style>
